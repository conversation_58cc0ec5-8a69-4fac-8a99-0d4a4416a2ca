import { DataSource } from 'typeorm';
import { RequirementStatus } from '../../entities/requirement-status.entity';

export async function seedRequirementStatuses(dataSource: DataSource): Promise<void> {
  const repository = dataSource.getRepository(RequirementStatus);

  const statuses = [
    {
      id: 'draft',
      description: 'The requirement has been uploaded but not yet processed.',
    },
    {
      id: 'reviewed_by_ai',
      description: 'AI analysis is complete and awaiting user review.',
    },
    {
      id: 'revised',
      description: 'The requirement has been modified by the user after AI review.',
    },
    {
      id: 'approved',
      description: 'The user has approved the requirement.',
    },
    {
      id: 'published',
      description: 'The requirement is finalized and ready for use.',
    },
    {
      id: 'rejected',
      description: 'The user has rejected the requirement.',
    },
  ];

  for (const status of statuses) {
    const existingStatus = await repository.findOne({ where: { id: status.id } });
    if (!existingStatus) {
      await repository.save(repository.create(status));
      console.log(`Created requirement status: ${status.id}`);
    }
  }
}
