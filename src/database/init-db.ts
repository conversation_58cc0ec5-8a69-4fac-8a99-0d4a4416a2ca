import { DataSource } from 'typeorm';
import { seedRequirementStatuses } from './seeds/requirement-statuses.seed';
import * as entities from '../entities';

async function initializeDatabase() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT ?? '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'requirement_service',
    entities: Object.values(entities),
    synchronize: true, // Only for development
  });

  try {
    await dataSource.initialize();
    console.log('Database connection established');

    // Seed requirement statuses
    await seedRequirementStatuses(dataSource);
    console.log('Database seeding completed');

    await dataSource.destroy();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error during database initialization:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  initializeDatabase();
}
