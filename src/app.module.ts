import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { AuthModule } from './auth/auth.module';
import { ExpressAdapter } from '@bull-board/express';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { Queue } from 'bullmq';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT ?? '5432', 10),
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_DATABASE || 'requirement_service',
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get('REDIS_DB', 21),
        },
      }),
      inject: [ConfigService],
    }),
    AuthModule
  ],
  providers: [
    {
      provide: 'BULL_BOARD',
      useFactory: async () => {
        const serverAdapter = new ExpressAdapter();
        serverAdapter.setBasePath('/queues');

        const embeddingsQueue = new Queue('embeddings', {
          connection: {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_PASSWORD,
            db: parseInt(process.env.REDIS_DB || '21'),
          }
        });

        const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
          queues: [new BullMQAdapter(embeddingsQueue)],
          serverAdapter: serverAdapter,
        });

        return {
          serverAdapter,
          addQueue,
          removeQueue,
          setQueues,
          replaceQueues,
        };
      },
    },
  ],
})
export class AppModule {}