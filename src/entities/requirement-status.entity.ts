import { <PERSON><PERSON>ty, PrimaryColumn, Column, OneToMany } from 'typeorm';
import { Requirement } from './requirement.entity';

@Entity('requirement_statuses')
export class RequirementStatus {
  @PrimaryColumn({ type: 'varchar', length: 50 })
  id: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Relations
  @OneToMany(() => Requirement, requirement => requirement.status)
  requirements: Requirement[];
}
