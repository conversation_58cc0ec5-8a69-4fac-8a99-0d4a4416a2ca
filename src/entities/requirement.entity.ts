import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { RequirementStatus } from './requirement-status.entity';
import { RequirementRevision } from './requirement-revision.entity';
import { AiAnalysis } from './ai-analysis.entity';

@Entity('requirements')
export class Requirement {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50, unique: true, name: 'agentq_id' })
  agentqId: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'varchar', length: 50, name: 'status_id' })
  statusId: string;

  @Column({ type: 'bigint', name: 'uploaded_by', nullable: true })
  uploadedById: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => RequirementStatus, status => status.requirements)
  @JoinColumn({ name: 'status_id' })
  status: RequirementStatus;

  @ManyToOne(() => User, user => user.requirements)
  @JoinColumn({ name: 'uploaded_by' })
  uploadedBy: User;

  @OneToMany(() => RequirementRevision, revision => revision.requirement)
  revisions: RequirementRevision[];

  @OneToMany(() => AiAnalysis, analysis => analysis.requirement)
  analyses: AiAnalysis[];
}
