import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RequirementStatus } from '../entities/requirement-status.entity';

@Injectable()
export class RequirementStatusSeederService {
  constructor(
    @InjectRepository(RequirementStatus)
    private requirementStatusRepository: Repository<RequirementStatus>,
  ) {}

  async seed(): Promise<void> {
    const statuses = [
      {
        id: 'draft',
        description: 'The requirement has been uploaded but not yet processed.',
      },
      {
        id: 'reviewed_by_ai',
        description: 'AI analysis is complete and awaiting user review.',
      },
      {
        id: 'revised',
        description: 'The requirement has been modified by the user after AI review.',
      },
      {
        id: 'approved',
        description: 'The user has approved the requirement.',
      },
      {
        id: 'published',
        description: 'The requirement is finalized and ready for use.',
      },
      {
        id: 'rejected',
        description: 'The user has rejected the requirement.',
      },
    ];

    for (const status of statuses) {
      const existingStatus = await this.requirementStatusRepository.findOne({ 
        where: { id: status.id } 
      });
      
      if (!existingStatus) {
        await this.requirementStatusRepository.save(
          this.requirementStatusRepository.create(status)
        );
        console.log(`✅ Created requirement status: ${status.id}`);
      }
    }
    
    console.log('✅ Requirement statuses seeding completed');
  }
}
