import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Param,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { RequirementsService } from './requirements.service';
import {
  UploadRequirementDto,
  QueryRequirementsDto,
  UploadResponseDto,
  PaginatedRequirementsResponseDto,
  RequirementResponseDto,
} from './dto';

@ApiTags('requirements')
@Controller('api/v1/requirements')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RequirementsController {
  constructor(private readonly requirementsService: RequirementsService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a new requirement document' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'File uploaded successfully and is being processed asynchronously',
    type: UploadResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or missing required fields',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async uploadRequirement(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: UploadRequirementDto,
  ): Promise<UploadResponseDto> {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // Validate file type
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'text/html',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Unsupported file type. Supported types: PDF, Word, Text, Markdown, HTML, Excel, CSV'
      );
    }

    // Extract text content from file
    const fileContent = this.extractTextFromFile(file.buffer, file.mimetype, file.originalname);

    const result = await this.requirementsService.uploadRequirement(
      uploadDto,
      fileContent,
    );

    return result;
  }

  @Get()
  @ApiOperation({ summary: 'List all requirements with filtering and pagination' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by requirement status' })
  @ApiQuery({ name: 'project_id', required: false, description: 'Filter by project ID' })
  @ApiQuery({ name: 'sort_by', required: false, description: 'Sort by field' })
  @ApiQuery({ name: 'order', required: false, description: 'Sort order (ASC/DESC)' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of requirements retrieved successfully',
    type: PaginatedRequirementsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async findAll(@Query() queryDto: QueryRequirementsDto): Promise<PaginatedRequirementsResponseDto> {
    return this.requirementsService.findAll(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific requirement by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Requirement retrieved successfully',
    type: RequirementResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Requirement not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async findOne(@Param('id') id: string): Promise<RequirementResponseDto> {
    return this.requirementsService.findOne(id);
  }

  private extractTextFromFile(buffer: Buffer, mimetype: string, originalname: string): string {
    // For now, return a simple text representation
    // In a real implementation, you would use libraries like:
    // - mammoth for .docx files
    // - pdf-parse for PDF files
    // - xlsx for Excel files
    // etc.
    
    if (mimetype.includes('text/') || originalname.endsWith('.md') || originalname.endsWith('.txt')) {
      return buffer.toString('utf-8');
    }
    
    // For other file types, return a placeholder
    return `[File content extracted from ${originalname}]\n\nThis is a placeholder for the actual file content that would be extracted using appropriate libraries for ${mimetype} files.`;
  }
}
