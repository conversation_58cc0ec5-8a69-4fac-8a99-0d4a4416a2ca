import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Requirement, User, RequirementStatus } from '../entities';
import { UploadRequirementDto, QueryRequirementsDto } from './dto';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';

@Injectable()
export class RequirementsService {
  constructor(
    @InjectRepository(Requirement)
    private requirementRepository: Repository<Requirement>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(RequirementStatus)
    private requirementStatusRepository: Repository<RequirementStatus>,
    private googleCloudStorageService: GoogleCloudStorageService,
  ) {}

  async uploadRequirement(
    uploadDto: UploadRequirementDto,
    fileContent: string,
    fileBuffer: Buffer,
    originalFilename: string,
    mimetype: string,
  ): Promise<{ requirement_id: string; agentq_id: string; status: string; message: string }> {
    // Find or create user
    let user = await this.userRepository.findOne({
      where: { userId: uploadDto.user_id }
    });

    if (!user) {
      user = this.userRepository.create({
        userId: uploadDto.user_id,
        companyId: uploadDto.company_id,
        projectId: uploadDto.project_id,
      });
      await this.userRepository.save(user);
    }

    // Get draft status ID
    const draftStatus = await this.requirementStatusRepository.findOne({
      where: { name: 'draft' }
    });

    if (!draftStatus) {
      throw new Error('Draft status not found. Please ensure requirement statuses are seeded.');
    }

    // Generate AgentQ ID (simple counter-based for now)
    const count = await this.requirementRepository.count();
    const agentqId = `R-${String(count + 1).padStart(3, '0')}`;

    // Upload file to Google Cloud Storage
    const storageUrl = await this.googleCloudStorageService.uploadRequirementFile(
      fileBuffer,
      originalFilename,
      mimetype,
      agentqId,
    );

    // Create requirement
    const requirement = this.requirementRepository.create({
      agentqId,
      name: uploadDto.name,
      content: fileContent,
      statusId: draftStatus.id,
      storageUrl,
      uploadedById: user.id,
    });

    const savedRequirement = await this.requirementRepository.save(requirement);

    return {
      requirement_id: savedRequirement.id,
      agentq_id: savedRequirement.agentqId,
      status: 'draft',
      message: 'File uploaded successfully and is being processed asynchronously'
    };
  }

  async findAll(queryDto: QueryRequirementsDto): Promise<{
    data: any[];
    pagination: { page: number; limit: number; total: number; totalPages: number };
  }> {
    const { status, project_id, sort_by = 'created_at', order = 'DESC', page = 1, limit = 10 } = queryDto;

    const queryBuilder = this.requirementRepository.createQueryBuilder('requirement')
      .leftJoinAndSelect('requirement.status', 'status')
      .leftJoinAndSelect('requirement.uploadedBy', 'user')
      .select([
        'requirement.id',
        'requirement.agentqId',
        'requirement.name',
        'requirement.statusId',
        'requirement.storageUrl',
        'requirement.createdAt',
        'requirement.updatedAt',
        'status.id',
        'status.name',
        'user.userId'
      ]);

    // Apply filters
    if (status) {
      queryBuilder.andWhere('status.name = :status', { status });
    }

    if (project_id) {
      queryBuilder.andWhere('user.projectId = :projectId', { projectId: project_id });
    }

    // Apply sorting
    const sortField = sort_by === 'status' ? 'requirement.statusId' : `requirement.${sort_by}`;
    queryBuilder.orderBy(sortField, order);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [requirements, total] = await queryBuilder.getManyAndCount();

    const data = requirements.map(req => ({
      id: req.id,
      agentq_id: req.agentqId,
      name: req.name,
      status: req.status?.name,
      storage_url: req.storageUrl,
      uploaded_by: req.uploadedBy?.userId,
      created_at: req.createdAt,
      updated_at: req.updatedAt,
    }));

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<any> {
    const requirement = await this.requirementRepository.findOne({
      where: { id },
      relations: ['status', 'uploadedBy'],
    });

    if (!requirement) {
      throw new NotFoundException(`Requirement with ID ${id} not found`);
    }

    return {
      id: requirement.id,
      agentq_id: requirement.agentqId,
      name: requirement.name,
      content: requirement.content,
      status: requirement.status?.name,
      storage_url: requirement.storageUrl,
      uploaded_by: requirement.uploadedBy?.userId,
      created_at: requirement.createdAt,
      updated_at: requirement.updatedAt,
    };
  }


}
