import { IsOptional, IsString, <PERSON>U<PERSON><PERSON>, IsIn, IsN<PERSON><PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class QueryRequirementsDto {
  @ApiPropertyOptional({ 
    description: 'Filter by requirement status',
    enum: ['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected']
  })
  @IsOptional()
  @IsString()
  @IsIn(['draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'])
  status?: string;

  @ApiPropertyOptional({ description: 'Filter by project ID' })
  @IsOptional()
  @IsUUID()
  project_id?: string;

  @ApiPropertyOptional({ 
    description: 'Sort by field',
    enum: ['created_at', 'updated_at', 'status', 'name']
  })
  @IsOptional()
  @IsString()
  @IsIn(['created_at', 'updated_at', 'status', 'name'])
  sort_by?: string;

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['ASC', 'DESC']
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  order?: 'ASC' | 'DESC';

  @ApiPropertyOptional({ description: 'Page number for pagination', default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 1)
  @IsNumber()
  page?: number;

  @ApiPropertyOptional({ description: 'Number of items per page', default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 10)
  @IsNumber()
  limit?: number;
}
