import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequirementsController } from './requirements.controller';
import { RequirementsService } from './requirements.service';
import { 
  User, 
  RequirementStatus, 
  Requirement, 
  RequirementRevision, 
  AiAnalysis 
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      RequirementStatus,
      Requirement,
      RequirementRevision,
      AiAnalysis,
    ]),
  ],
  controllers: [RequirementsController],
  providers: [RequirementsService],
  exports: [RequirementsService],
})
export class RequirementsModule {}
