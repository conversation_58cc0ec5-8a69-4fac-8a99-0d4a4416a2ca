import { Module, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequirementsController } from './requirements.controller';
import { RequirementsService } from './requirements.service';
import { RequirementStatusSeederService } from './requirement-status-seeder.service';
import { GoogleCloudStorageService } from '../storage/google-cloud-storage.service';
import {
  User,
  RequirementStatus,
  Requirement,
  RequirementRevision,
  AiAnalysis
} from '../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      RequirementStatus,
      Requirement,
      RequirementRevision,
      AiAnalysis,
    ]),
  ],
  controllers: [RequirementsController],
  providers: [RequirementsService, RequirementStatusSeederService, GoogleCloudStorageService],
  exports: [RequirementsService],
})
export class RequirementsModule implements OnModuleInit {
  constructor(private readonly seederService: RequirementStatusSeederService) {}

  async onModuleInit() {
    await this.seederService.seed();
  }
}
