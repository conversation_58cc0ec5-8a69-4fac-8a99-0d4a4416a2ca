# Requirements Management API

This document describes the implementation of the Requirements Management API endpoints and database structure.

## API Endpoints

### 1. POST /api/v1/requirements/upload

**Purpose**: Upload a new requirement document. This is the entry point for the entire feature.

**Request**:
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Authentication**: Bearer JWT token required

**Body Parameters**:
```typescript
{
  name: string;           // Name of the requirement document
  project_id: string;     // UUID of the project
  company_id: string;     // UUID of the company
  user_id: string;        // UUID of the user
  file: File;            // The requirement document file
}
```

**Supported File Types**:
- Word Documents (.doc, .docx)
- Markdown (.md)
- Text Files (.txt)
- PDF Documents (.pdf)
- HTML Files (.html)
- Excel Spreadsheets (.xls, .xlsx)
- CSV Files (.csv)

**Response** (202 Accepted):
```json
{
  "requirement_id": "uuid",
  "agentq_id": "R-001",
  "status": "draft",
  "message": "File uploaded successfully and is being processed asynchronously"
}
```

**New Features**:
- ✅ **Google Cloud Storage Integration**: Files are automatically uploaded to GCS in the `requirements/` folder
- ✅ **Storage URL Tracking**: Each requirement includes a `storage_url` field with the GCS public URL
- ✅ **Improved Database Schema**: Auto-increment IDs for requirement statuses

### 2. GET /api/v1/requirements

**Purpose**: List all requirements with filtering options and pagination.

**Request**:
- **Method**: GET
- **Authentication**: Bearer JWT token required

**Query Parameters**:
```typescript
{
  status?: 'draft' | 'reviewed_by_ai' | 'revised' | 'approved' | 'published' | 'rejected';
  project_id?: string;        // Filter by project UUID
  sort_by?: 'created_at' | 'updated_at' | 'status' | 'name';
  order?: 'ASC' | 'DESC';
  page?: number;              // Default: 1
  limit?: number;             // Default: 10
}
```

**Response** (200 OK):
```json
{
  "data": [
    {
      "id": "uuid",
      "agentq_id": "R-001",
      "name": "User Authentication Requirements",
      "status": "draft",
      "storage_url": "https://storage.googleapis.com/your-bucket/requirements/R-001_uuid.pdf",
      "uploaded_by": "user-uuid",
      "created_at": "2025-01-07T10:00:00Z",
      "updated_at": "2025-01-07T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

### 3. GET /api/v1/requirements/:id

**Purpose**: Get a specific requirement by ID with full content.

**Request**:
- **Method**: GET
- **Authentication**: Bearer JWT token required

**Response** (200 OK):
```json
{
  "id": "uuid",
  "agentq_id": "R-001",
  "name": "User Authentication Requirements",
  "content": "# User Authentication\n\nThe system shall...",
  "status": "draft",
  "storage_url": "https://storage.googleapis.com/your-bucket/requirements/R-001_uuid.pdf",
  "uploaded_by": "user-uuid",
  "created_at": "2025-01-07T10:00:00Z",
  "updated_at": "2025-01-07T10:00:00Z"
}
```

## Database Schema

The database consists of 5 main tables:

### 1. users
Stores user information for tracking who uploaded requirements.

### 2. requirement_statuses
Lookup table for requirement statuses with auto-increment ID and the following predefined values:
- `id`: Auto-increment primary key (INTEGER)
- `name`: Status name (VARCHAR) - 'draft', 'reviewed_by_ai', 'revised', 'approved', 'published', 'rejected'
- `description`: Human-readable description

### 3. requirements
Main table storing requirement documents with their current state, content, and Google Cloud Storage URL.

### 4. requirement_revisions
Tracks the history of changes to requirements for audit trail and version control.

### 5. ai_analyses
Stores AI analysis results including quality scores and structured feedback in JSONB format.

## File Structure

```
src/
├── entities/
│   ├── user.entity.ts
│   ├── requirement-status.entity.ts
│   ├── requirement.entity.ts
│   ├── requirement-revision.entity.ts
│   ├── ai-analysis.entity.ts
│   └── index.ts
├── requirements/
│   ├── dto/
│   │   ├── upload-requirement.dto.ts
│   │   ├── query-requirements.dto.ts
│   │   ├── requirement-response.dto.ts
│   │   └── index.ts
│   ├── requirements.controller.ts
│   ├── requirements.service.ts
│   ├── requirements.module.ts
│   └── requirement-status-seeder.service.ts
├── storage/
│   └── google-cloud-storage.service.ts
└── database/
    ├── seeds/
    │   └── requirement-statuses.seed.ts
    └── init-db.ts
```

## Setup Instructions

1. **Install Dependencies**: All required dependencies are already included in package.json

2. **Database Setup**: Run the SQL schema from `database-schema.sql` or use the TypeORM synchronization

3. **Environment Variables**: Ensure the following environment variables are set:
   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_USERNAME=postgres
   DB_PASSWORD=postgres
   DB_DATABASE=requirement_service
   JWT_SECRET=your-jwt-secret

   # Google Cloud Storage Configuration
   GOOGLE_CLOUD_PROJECT_ID=your-project-id
   GOOGLE_CLOUD_CLIENT_EMAIL=<EMAIL>
   GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
   GOOGLE_CLOUD_BUCKET=your-bucket-name
   ```

4. **Database Migration**: If you have existing data, run the migration script:
   ```bash
   psql -d requirement_service -f migration-to-new-schema.sql
   ```

   Or for fresh installations, the app will automatically seed requirement statuses on startup.

5. **Start the Application**:
   ```bash
   npm run start:dev
   ```

## API Documentation

Once the application is running, you can access the Swagger documentation at:
- http://localhost:3002/api

## Authentication

All endpoints require JWT authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK`: Successful GET requests
- `202 Accepted`: Successful file upload
- `400 Bad Request`: Invalid request data or unsupported file type
- `401 Unauthorized`: Missing or invalid JWT token
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side errors

## Future Enhancements

1. **File Processing**: Implement actual file content extraction using libraries like mammoth, pdf-parse, etc.
2. **AI Integration**: Add actual AI analysis functionality
3. **Async Processing**: Implement background job processing for file analysis
4. **File Storage**: Integrate with cloud storage for file persistence
5. **Webhooks**: Add webhook support for status change notifications
